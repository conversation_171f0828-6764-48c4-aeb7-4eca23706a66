package qidian.it.config;

import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.AnnotationConfigWebApplicationContext;
import org.springframework.web.servlet.support.AbstractDispatcherServletInitializer;

//4.创建Servlet容器的初始化类，在里面加载springMvcConfig
public class ServletContainersInitConfig extends AbstractDispatcherServletInitializer {
    //加载SpringMvc容器配置
    protected WebApplicationContext createServletApplicationContext() {
        AnnotationConfigWebApplicationContext ctx = new AnnotationConfigWebApplicationContext();
        ctx.register(SpringMvcConfig.class);
        return ctx;
    }

    //设置哪些请求会交给springMvc处理
    protected String[] getServletMappings() {
        return new String[]{"/"};
    }
    //加载Spring父容器配置
    protected WebApplicationContext createRootApplicationContext() {
        return null;
    }
}
