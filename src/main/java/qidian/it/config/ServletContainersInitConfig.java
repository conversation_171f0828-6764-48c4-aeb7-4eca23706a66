package qidian.it.config;

import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer;

import javax.servlet.Filter;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;

//4.创建Servlet容器的初始化类，在里面加载springMvcConfig
public class ServletContainersInitConfig extends AbstractAnnotationConfigDispatcherServletInitializer {
    //简化写法
    protected Class<?>[] getRootConfigClasses() {
        return new Class[0];
    }
    protected Class<?>[] getServletConfigClasses() {
        return new Class[]{SpringMvcConfig.class};
    }
    protected String[] getServletMappings() {
        return new String[]{"/"};
    }

    //乱码处理
    @Override
    protected Filter[] getServletFilters() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        filter.setEncoding("UTF-8");
        filter.setForceEncoding(true);
        filter.setForceRequestEncoding(true);
        filter.setForceResponseEncoding(true);
        return new Filter[]{filter};
    }

    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        super.onStartup(servletContext);
        // 设置初始化参数
        servletContext.setInitParameter("defaultHtmlEscape", "true");
        servletContext.setInitParameter("spring.http.encoding.charset", "UTF-8");
        servletContext.setInitParameter("spring.http.encoding.enabled", "true");
        servletContext.setInitParameter("spring.http.encoding.force", "true");
    }

//    //加载SpringMvc容器配置
//    protected WebApplicationContext createServletApplicationContext() {
//        AnnotationConfigWebApplicationContext ctx = new AnnotationConfigWebApplicationContext();
//        ctx.register(SpringMvcConfig.class);
//        return ctx;
//    }
//
//    //设置哪些请求会交给springMvc处理
//    protected String[] getServletMappings() {
//        return new String[]{"/"};
//    }
//    //加载Spring父容器配置
//    protected WebApplicationContext createRootApplicationContext() {
//        return null;
//    }
}
