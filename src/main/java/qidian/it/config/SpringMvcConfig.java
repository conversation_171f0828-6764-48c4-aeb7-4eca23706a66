package qidian.it.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

//3.创建springMvc的配置类，在里面加载控制类
@Configuration
@ComponentScan("qidian.it.controller")
@EnableWebMvc//开启springMvc功能
public class SpringMvcConfig implements WebMvcConfigurer {

    // 配置静态资源处理
    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }
}
