package qidian.it.controller;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import qidian.it.domain.Book;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/book")
public class BookController {
    @RequestMapping("/save")
    @ResponseBody
    public String save(){
        System.out.println("book save ...");
        return "success";
    }
    @RequestMapping("/delete")
    @ResponseBody
    public String delete(){
        System.out.println("book delete ...");
        return "success";
    }
    //日期参数
    @RequestMapping("/dateParam")
    @ResponseBody
    public String dateParam(@DateTimeFormat(pattern = "yyyy-MM-dd") Date date){
        System.out.println("Parameter date ==> "+date);
        return "success";
    }

    //响应页面
    @RequestMapping("/responsePage")
    public String responsePage(){
        System.out.println("book response ...");
        return "index.jsp";
    }

    //响应文本数据
    @RequestMapping("/responseText")
    @ResponseBody
    public String responseText(){
        System.out.println("book response ...");
        return "response text message";
    }
    //响应POJO
    @RequestMapping("/responsePOJO")
    @ResponseBody
    public Book responsePOJO(){
        System.out.println("book response ...");
        return new Book(1,"SpringMVC","Qidian");
    }

    //响应pojo集合
    @RequestMapping("/responsePOJOList")
    @ResponseBody
    public List<Book> responsePOJOList(){
        System.out.println("返回json集合数据");
        Book book1 = new Book(1,"SpringMVC","Qidian");
        Book book2 = new Book(2,"SpringMVC","Qidian");
        List<Book> books = new ArrayList<>();
        books.add(book1);
        books.add(book2);
        return books;

    }

}
