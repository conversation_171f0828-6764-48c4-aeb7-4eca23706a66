package qidian.it.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

//控制器的制作：
//1.创建一个控制器类，并使用@Controller注解进行标识，声明成bean
//2.创建处理方法，并使用@RequestMapping注解进行标识，处理用户请求
//3.处理方法的返回值可以是String类型，表示视图名称
//4.处理方法的参数可以是POJO类型，自动进行数据绑定
@Controller
@RequestMapping("/user")
public class UserController {
    //设置访问的路径
    @RequestMapping("/save")
    //设置返回值
    @ResponseBody
    public String save( ){
        System.out.println("UserController.save()");
        return "{'module':'success'}";
    }

    @RequestMapping("/delete")
    @ResponseBody
    public String delete( ){
        System.out.println("UserController.delete()");
        return "{'module':'success'}";
    }

    @RequestMapping("/find")
    @ResponseBody
    public String find(HttpServletRequest request) throws Exception {
        // 直接从request获取参数并处理编码
        request.setCharacterEncoding("UTF-8");
        String name = request.getParameter("name");
        String ageStr = request.getParameter("age");
        Integer age = ageStr != null ? Integer.parseInt(ageStr) : null;

        System.out.println("Direct parameter name ==> " + name);
        System.out.println("Direct parameter age ==> " + age);
        return "{'module':'success'}";
    }

    @RequestMapping("/find2")
    @ResponseBody
    public String find2(@RequestParam(value = "name", required = false) String name,
                       @RequestParam(value = "age", required = false) Integer age) {
        System.out.println("@RequestParam name ==> " + name);
        System.out.println("@RequestParam age ==> " + age);
        return "{'module':'success'}";
    }

    @RequestMapping(value = "/findPost", method = RequestMethod.POST)
    @ResponseBody
    public String findPost(String name, Integer age) {
        System.out.println("POST Parameter name ==> " + name);
        System.out.println("POST Parameter age ==> " + age);
        return "{'module':'success'}";
    }
}

