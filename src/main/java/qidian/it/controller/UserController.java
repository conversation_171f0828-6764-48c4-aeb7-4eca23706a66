package qidian.it.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

//控制器的制作：
//1.创建一个控制器类，并使用@Controller注解进行标识，声明成bean
//2.创建处理方法，并使用@RequestMapping注解进行标识，处理用户请求
//3.处理方法的返回值可以是String类型，表示视图名称
//4.处理方法的参数可以是POJO类型，自动进行数据绑定
@Controller
@RequestMapping("/user")
public class UserController {
    //设置访问的路径
    @RequestMapping("/save")
    //设置返回值
    @ResponseBody
    public String save( ){
        System.out.println("UserController.save()");
        return "{'module':'success'}";
    }

    @RequestMapping("/delete")
    @ResponseBody
    public String delete( ){
        System.out.println("UserController.delete()");
        return "{'module':'success'}";
    }

    @RequestMapping("/find")
    @ResponseBody
    public String find(String name, Integer age) throws Exception {
        // 手动处理中文编码
        if (name != null) {
            name = new String(name.getBytes("ISO-8859-1"), "UTF-8");
        }
        System.out.println("Parameter name ==> " + name);
        System.out.println("Parameter age ==> " + age);
        return "{'module':'success'}";
    }

    @RequestMapping(value = "/findPost", method = RequestMethod.POST)
    @ResponseBody
    public String findPost(String name, Integer age) {
        System.out.println("POST Parameter name ==> " + name);
        System.out.println("POST Parameter age ==> " + age);
        return "{'module':'success'}";
    }
}

