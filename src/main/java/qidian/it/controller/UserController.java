package qidian.it.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import qidian.it.domain.User;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

//控制器的制作：
//1.创建一个控制器类，并使用@Controller注解进行标识，声明成bean
//2.创建处理方法，并使用@RequestMapping注解进行标识，处理用户请求
//3.处理方法的返回值可以是String类型，表示视图名称
//4.处理方法的参数可以是POJO类型，自动进行数据绑定
@Controller
@ResponseBody
//合二为一叫
@RestController
@RequestMapping("/users")
public class UserController {
    //设置访问的路径
    @PostMapping
    //设置返回值
    //@RequestBody用来接收json数据
    public String save(@RequestBody User user){
        System.out.println("User save===>"+ user);
        return "{'module':'user save success'}";
    }

    @DeleteMapping("/{id}")
    public String delete(@PathVariable Integer id){
        System.out.println("User delete ..."+ id);
        return "{'module':'user delete'}";
    }

    @GetMapping
    public List<User> selectAll(){
        System.out.println("User selectAll ===>");
        List<User> list = new ArrayList<>();
        list.add(new User(1,"admin",18,"男"));
        list.add(new User(2,"admin2",19,"女"));
        list.add(new User(3,"admin3",20,"男"));
        return list;
    }

//    //数组
//    @RequestMapping("/findList")
//    @ResponseBody
//    public String findList(String[] names){
//        System.out.println("Parameter names ==> "+ Arrays.toString(names));
//        return "{'module':'success'}";
//    }
//
//    //集合参数
//    @RequestMapping("/listParam")
//    @ResponseBody
//    public String findList2(@RequestBody List<String> names){
//        System.out.println("Parameter names ==> "+ names);
//        return "{'module':'success'}";
//    }
}

