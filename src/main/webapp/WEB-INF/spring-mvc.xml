<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd">

    <!-- 字符编码过滤器 -->
    <bean id="characterEncodingFilter" class="org.springframework.web.filter.CharacterEncodingFilter">
        <property name="encoding" value="UTF-8"/>
        <property name="forceEncoding" value="true"/>
    </bean>

    <!-- 其他Spring MVC配置 -->

</beans>
