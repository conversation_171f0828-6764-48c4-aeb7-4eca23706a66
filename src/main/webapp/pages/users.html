<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .form-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .form-group {
            flex: 1;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        input[type="text"]:focus, input[type="number"]:focus, select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .search-section {
            margin-bottom: 20px;
        }
        
        .table-section {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            display: none;
        }
        
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .actions {
            white-space: nowrap;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户管理系统</h1>
        
        <!-- 消息提示 -->
        <div id="message" class="message"></div>
        
        <!-- 用户表单 -->
        <div class="form-section">
            <h3>添加/编辑用户</h3>
            <form id="userForm">
                <input type="hidden" id="userId" name="id">
                <div class="form-row">
                    <div class="form-group">
                        <label for="userName">姓名:</label>
                        <input type="text" id="userName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="userAge">年龄:</label>
                        <input type="number" id="userAge" name="age" min="1" max="120" required>
                    </div>
                    <div class="form-group">
                        <label for="userSex">性别:</label>
                        <select id="userSex" name="sex" required>
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" id="submitBtn">添加用户</button>
                        <button type="button" class="btn btn-warning" id="cancelBtn" style="display:none;">取消编辑</button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 搜索功能 -->
        <div class="search-section">
            <div class="form-row">
                <div class="form-group">
                    <label for="searchName">搜索用户:</label>
                    <input type="text" id="searchName" placeholder="输入姓名搜索...">
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button type="button" class="btn btn-success" onclick="searchUser()">搜索</button>
                    <button type="button" class="btn btn-primary" onclick="loadAllUsers()">显示全部</button>
                </div>
            </div>
        </div>
        
        <!-- 用户列表 -->
        <div class="table-section">
            <h3>用户列表</h3>
            <table id="userTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>年龄</th>
                        <th>性别</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="userTableBody">
                    <!-- 用户数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 基础URL配置
        const BASE_URL = '/springmvc_01_war_exploded/users';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAllUsers();
            
            // 绑定表单提交事件
            document.getElementById('userForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveUser();
            });
            
            // 绑定取消按钮事件
            document.getElementById('cancelBtn').addEventListener('click', function() {
                cancelEdit();
            });
        });
        
        // 显示消息
        function showMessage(message, type = 'success') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = message;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
        }
        
        // 加载所有用户
        function loadAllUsers() {
            fetch(`${BASE_URL}`)
                .then(response => response.json())
                .then(data => {
                    displayUsers(data);
                })
                .catch(error => {
                    console.error('加载用户失败:', error);
                    showMessage('加载用户列表失败', 'error');
                });
        }

        // 显示用户列表
        function displayUsers(users) {
            const tbody = document.getElementById('userTableBody');
            tbody.innerHTML = '';

            if (users && users.length > 0) {
                users.forEach(user => {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${user.id || 'N/A'}</td>
                        <td>${user.name || ''}</td>
                        <td>${user.age || ''}</td>
                        <td>${user.sex || ''}</td>
                        <td class="actions">
                            <button class="btn btn-warning btn-sm" onclick="editUser(${user.id}, '${user.name}', ${user.age}, '${user.sex}')">编辑</button>
                            <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.id})">删除</button>
                        </td>
                    `;
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">暂无用户数据</td></tr>';
            }
        }

        // 保存用户
        function saveUser() {
            const form = document.getElementById('userForm');
            const userId = document.getElementById('userId').value;
            const name = document.getElementById('userName').value;
            const age = parseInt(document.getElementById('userAge').value);
            const sex = document.getElementById('userSex').value;

            const userData = {
                name: name,
                age: age,
                sex: sex
            };

            const url = BASE_URL;
            let method = 'POST';

            if (userId) {
                userData.id = parseInt(userId);
                method = 'PUT';
            }

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.module && data.module.includes('success')) {
                    showMessage(userId ? '用户更新成功' : '用户添加成功');
                    form.reset();
                    document.getElementById('userId').value = '';
                    document.getElementById('submitBtn').textContent = '添加用户';
                    document.getElementById('cancelBtn').style.display = 'none';
                    loadAllUsers();
                } else {
                    showMessage('操作失败', 'error');
                }
            })
            .catch(error => {
                console.error('保存用户失败:', error);
                showMessage('保存用户失败', 'error');
            });
        }

        // 编辑用户
        function editUser(id, name, age, sex) {
            document.getElementById('userId').value = id;
            document.getElementById('userName').value = name;
            document.getElementById('userAge').value = age;
            document.getElementById('userSex').value = sex;
            document.getElementById('submitBtn').textContent = '更新用户';
            document.getElementById('cancelBtn').style.display = 'inline-block';
        }

        // 取消编辑
        function cancelEdit() {
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('submitBtn').textContent = '添加用户';
            document.getElementById('cancelBtn').style.display = 'none';
        }

        // 删除用户
        function deleteUser(id) {
            if (confirm('确定要删除这个用户吗？')) {
                fetch(`${BASE_URL}/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.module && data.module.includes('delete')) {
                        showMessage('用户删除成功');
                        loadAllUsers();
                    } else {
                        showMessage('删除失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('删除用户失败:', error);
                    showMessage('删除用户失败', 'error');
                });
            }
        }

        // 搜索用户（前端过滤）
        function searchUser() {
            const searchName = document.getElementById('searchName').value.trim();
            if (searchName) {
                // 先获取所有用户，然后在前端过滤
                fetch(`${BASE_URL}`)
                    .then(response => response.json())
                    .then(data => {
                        const filteredUsers = data.filter(user =>
                            user.name.toLowerCase().includes(searchName.toLowerCase())
                        );
                        displayUsers(filteredUsers);
                        showMessage(`找到 ${filteredUsers.length} 个用户`);
                    })
                    .catch(error => {
                        console.error('搜索用户失败:', error);
                        showMessage('搜索失败', 'error');
                    });
            } else {
                showMessage('请输入搜索关键词', 'error');
            }
        }
    </script>
</body>
</html>
